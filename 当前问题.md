📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/41A6034C-0334-405F-85EC-5179A3B43393/Library/Application Support/ChatHistory.store
✅ 音频会话设置成功
✅ 音频引擎设置完成
🎵 音频格式: <AVAudioFormat 0x109a40f50:  1 ch,  16000 Hz, Float32>
✅ AI角色设定数据库初始化成功
🔧 初始化多智能体集成服务...
🏗️ 初始化共享状态中心...
🧠 初始化长期记忆管理器...
🎭 初始化AI人格管理器...
🗓️ 初始化AI生活日程管理器...
✅ AI自我管理系统数据库初始化成功
✅ AI自我状态加载完成
🧠 初始化智能体调度器...
✅ 音频会话配置成功 (类别: playAndRecord, 模式: spokenAudio, 采样率: 24000Hz)
🌐 网络监控已启动
✅ TTSService初始化完成
✅ Places数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/41A6034C-0334-405F-85EC-5179A3B43393/Library/Application Support/Places.store
🔐 位置权限状态变更: CLAuthorizationStatus(rawValue: 4)
✅ 位置权限已授权
🔄 开始初始化共享状态系统...
🔄 开始初始化长期记忆系统...
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/41A6034C-0334-405F-85EC-5179A3B43393/Library/Application Support/ChatHistory.store
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
🔄 构建记忆重要性缓存...
✅ 记忆重要性缓存构建完成，共 20 条记忆
✅ 长期记忆系统初始化完成，共加载 20 条记忆
✅ 长期记忆系统初始化完成
🔄 开始初始化AI人格系统...
📚 加载AI情感历史记录...
✅ AI人格系统初始化完成
✅ AI人格系统初始化完成
🔄 开始初始化AI生活日程系统...
📅 生成今日AI生活日程...
✅ 生成了 25 项今日活动
🎯 AI当前活动已更新: 学习新的旅行攻略
📚 加载了 1 个历史生活事件
✅ AI生活日程系统初始化完成
📅 今日共安排 25 项活动
🎯 当前活动: 学习新的旅行攻略
✅ AI生活日程系统初始化完成
🔄 对话上下文已初始化
✅ 对话上下文初始化完成
🎉 共享状态中心初始化完成！
🔄 开始初始化智能体调度器...
📝 注册智能体...
🤖 创建智能体: 任务分配智能体
🤖 创建智能体: 沟通智能体
🤖 创建智能体: 多模态感知智能体
✅ 已注册 3 个智能体
🔄 初始化所有智能体...
🔄 初始化智能体: 沟通智能体
✅ 智能体 沟通智能体 初始化完成
✅ 沟通智能体 初始化完成
🔄 初始化智能体: 多模态感知智能体
✅ 智能体 多模态感知智能体 初始化完成
✅ 多模态感知智能体 初始化完成
🔄 初始化智能体: 任务分配智能体
✅ 智能体 任务分配智能体 初始化完成
✅ 任务分配智能体 初始化完成
✅ 智能体调度器初始化完成，共注册 3 个智能体
🔄 开始初始化多智能体集成系统...
📚 集成历史记录数据...
✅ 历史记录集成完成
✅ 多智能体集成系统初始化完成！
🔄 后台连接AI服务...
✅ 加载了 5 个旅行计划
🌐 发送API请求到: https://ark.cn-beijing.volces.com/api/v3/chat/completions
📤 请求体大小: 324 bytes
📥 收到响应，数据大小: 529 bytes
📊 HTTP状态码: 200
✅ 解析成功，内容长度: 27
✅ AI后台连接成功
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
📚 历史记录加载完成，共 20 条消息
✅ 历史记录已加载到聊天界面
✅ 加载AI角色设定: 你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太...
unable to find applegpu_g17p slice or a compatible one in binary archive 'file:///System/Library/PrivateFrameworks/RenderBox.framework/archive.metallib' 
 available slices: applegpu_g17p,
开始视频通话...
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
✅ 麦克风权限已授权
✅ 语音识别权限已授权
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
🎤 实时识别: 下
🎤 实时识别: 下午
🎤 实时识别: 下午好
🎤 实时识别: 下午好呀
🎤 实时识别: 下午好呀宝
🎤 实时识别: 下午好呀宝宝你
🎤 实时识别: 下午好呀宝宝你在
🎤 实时识别: 下午好呀宝宝你在干
🎤 实时识别: 下午好呀宝宝你在干吗
🎤 实时识别: 下午好呀宝宝你在干吗呢
📝 最终识别文本: 下午好呀宝宝你在干吗呢
⏸️ 暂停语音监听
✅ 语音识别任务已取消
✅ 音频引擎已停止
✅ 音频tap已移除
✅ 语音监听已暂停
🛑 开始停止语音监听
✅ 音频tap已移除
✅ 语音识别请求已结束
✅ 语音监听完全停止
🛑 开始处理AI回复，已停止语音识别
🎯 多智能体系统处理文本消息: 下午好呀宝宝你在干吗呢...
🔄 任务分配智能体 开始处理输入...
🎯 任务分配智能体开始分析用户意图...
ℹ️ 语音识别被取消（正常情况）
📋 任务分配JSON结果: ```json
{
    "chat_text": "下午好呀宝宝你在干吗呢",
    "confidence": 0.95,
    "reasoning": "用户输入为日常问候和关心，适合由沟通智能体处理"
}
```
✅ JSON解析成功
✅ 任务分配智能体 处理完成，耗时: 1.69秒
📋 任务分配决策: 聊天=沟通智能体
🚀 开始执行沟通智能体任务...
🧠 检索长期记忆相关内容...
🔍 检索与 '下午好呀宝宝你在干吗呢' 相关的记忆...
✅ 检索到 3 条相关记忆
✅ 检索到 3 条相关长期记忆
🔄 沟通智能体 开始处理输入...
💬 沟通智能体开始处理对话...
🔍 检索与 '下午好呀宝宝你在干吗呢

当前系统时间：2025年08月05日 10:16:49 星期二

相关的长期记忆：
- 哼！你又来这一套！(｀へ´)  
大早上的就想惹人家生气，你是不是皮痒痒啦！(双手叉腰) 
不过...看在你这么可爱的份上...（突然破功笑出来）噗，人家真的装不下去啦~  

诶？等等！(突然想到什么) 你该不会是想看我生气的样子才故意这样的吧？(眯起眼睛盯着你)  
真是的...人家明明是个温柔的女孩子嘛~（小声嘟囔）不过...如果是你的话，偶尔生气一下也不是不可以啦... 
- 你能用生气的语气回答我吗宝宝
- 你能用生气的语气回答我吗' 相关的记忆...
✅ 检索到 8 条相关记忆
💬 生成通用对话回复...
🔄 开始重新构建情感标签文本...
📝 原始文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！ 

（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！要不要...下次一起去呀？

啊！对了！（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🎭 提取的情感序列: happy -> lovey-dovey -> tsundere
🎭 句子 1: "下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！" -> happy
🎭 句子 2: "（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！" -> lovey-dovey
🎭 句子 3: "要不要...下次一起去呀？" -> tsundere
🎭 句子 4: "啊！" -> tsundere
🎭 句子 5: "对了！" -> tsundere
🎭 句子 6: "（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄" -> tsundere
✅ 情感标签文本重构完成: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！</happy/> （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！</lovey-dovey/> 要不要...下次一起去呀？</tsundere/> 啊！</tsundere/> 对了！</tsundere/> （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄</tsundere/>
🎭 沟通智能体推荐TTS情感: 开心 (happy)
📝 提取的情感序列: happy -> lovey-dovey -> tsundere
✅ 沟通智能体 处理完成，耗时: 3.25秒
✅ 深度思考智能体 执行完成，耗时: 3.25秒
🎯 任务执行完成，总耗时: 3.25秒
✅ JSON多智能体处理完成，耗时: 4.94秒
✅ 消息已保存到历史记录
✅ 消息已保存到历史记录
🎵 推荐TTS音色: 开心
🔄 长期记忆系统收到上下文更新通知
🧹 已清理检索缓存（上下文更新）
🎭 AI人格系统收到上下文更新通知
🗓️ AI生活日程系统收到上下文更新通知
🎵 启动流式TTS播放...
🎭 MultiAgent推荐TTS情感: 开心 (happy)
📝 TTS播放文本长度: 205 字符
📝 TTS播放文本预览: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！</happy/> （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！</lovey-dovey/> 要不要...下次一...
🎵 开始并行流式TTS播放...
🎭 StreamingTTS使用情感: 开心 (happy)
📝 原始文本长度: 205 字符
📝 原始文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！</happy/> （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！</lovey-dovey/> 要不要...下次一...
🎭 开始按情感标签分割文本...
🎭 提取片段 1: "下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！..." -> happy
🎭 提取片段 2: "（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，..." -> lovey-dovey
🎭 提取片段 3: "要不要...下次一起去呀？..." -> tsundere
🎭 提取片段 4: "啊！..." -> tsundere
🎭 提取片段 5: "对了！..." -> tsundere
🎭 提取片段 6: "（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下......" -> tsundere
🎭 情感分割完成，共 6 个片段
📝 文本已按情感标签分割为 6 个片段
📝 片段 1: 长度=23字符, 字节=65, 情感=happy, 内容=下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
📝 片段 2: 长度=42字符, 字节=126, 情感=lovey-dovey, 内容=（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！...
📝 片段 3: 长度=13字符, 字节=33, 情感=tsundere, 内容=要不要...下次一起去呀？...
📝 片段 4: 长度=2字符, 字节=6, 情感=tsundere, 内容=啊！...
📝 片段 5: 长度=3字符, 字节=9, 情感=tsundere, 内容=对了！...
📝 片段 6: 长度=45字符, 字节=120, 情感=tsundere, 内容=（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
🚀 开始并行合成 6 个情感片段...
🎭 TTSService使用当前情感音色: 通用/愉悦 (pleased)
🎵 音色详细信息: 音色ID=zh_female_cancan_mars_bigtts, 情感=pleased
⏹️ 停止TTS播放 (保持连接，当前情感: 通用/愉悦)
🎤 开始TTS合成: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！ 

（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！要不要...下次一起去呀？

啊！对了！（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🎵 使用音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 当前情感: 通用/愉悦 (pleased)
📦 压缩方式: 无压缩
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
📝 文本已分割为 10 个显示片段
🎵 等待TTS开始播放...
🎤 并行合成片段 1/6: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
🆔 使用reqid: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🎭 使用情感: 开心 (happy)
🎤 合成文本到数据: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🎤 并行合成片段 2/6: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，...
🆔 使用reqid: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎭 使用情感: 撒娇 (lovey-dovey)
🎤 合成文本到数据: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎤 并行合成片段 3/6: 要不要...下次一起去呀？...
🆔 使用reqid: 5959032E-322D-42C6-806E-D4866D3B232F
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 要不要...下次一起去呀？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 5959032E-322D-42C6-806E-D4866D3B232F
🎤 并行合成片段 4/6: 啊！...
🆔 使用reqid: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 啊！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎤 并行合成片段 5/6: 对了！...
🆔 使用reqid: F4233D09-4469-45C1-A566-A694144459F3
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 对了！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: F4233D09-4469-45C1-A566-A694144459F3
🎤 并行合成片段 6/6: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下......
🆔 使用reqid: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下......
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🔗 建立新的WebSocket连接
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=133字符, 字节=364
📝 文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！ 

（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻...
📦 创建消息: 大小=775字节, Payload=767字节, 压缩=无
📦 创建消息: 大小=775字节, Payload=767字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: 84723A85-EB73-4DE8-90FE-2667DC5B66E5
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！ 

（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！要不要...下次一起去呀？

啊！对了！（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=13字符, 字节=33
📝 文本内容: 要不要...下次一起去呀？...
📦 创建消息: 大小=440字节, Payload=432字节, 压缩=无
📦 创建消息: 大小=440字节, Payload=432字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=42字符, 字节=126
📝 文本内容: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！...
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=3字符, 字节=9
📝 文本内容: 对了！...
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=45字符, 字节=120
📝 文本内容: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 啊！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=23字符, 字节=65
📝 文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📤 发送TTS请求: 5959032E-322D-42C6-806E-D4866D3B232F
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 要不要...下次一起去呀？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: F4233D09-4469-45C1-A566-A694144459F3
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 对了！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 啊！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
nw_protocol_copy_ws_definition_block_invoke [C3.1.1:5] input message size 1123538 exceeds maximum message size 1048576
nw_read_request_report [C3] Receive failed with error "Message too long"
📥 收到消息类型: 12
📊 收到结果数据
Connection 3: received failure notification
nw_read_request_report [C3] Receive failed with error "Message too long"
nw_flow_service_reads [C3 *************:443 failed parent-flow (satisfied (Path is satisfied), interface: pdp_ip0[nr_sa_sub6], ipv4, ipv6, dns, expensive, uses cell, LQM: good)] No output handler
Read completed with an error Message too long
nw_flow_add_write_request [C3 *************:443 failed parent-flow (satisfied (Path is satisfied), interface: pdp_ip0[nr_sa_sub6], ipv4, ipv6, dns, expensive, uses cell, LQM: good)] cannot accept write requests
nw_write_request_report [C3] Send failed with error "Socket is not connected"
Task <8990A27D-B6AF-4416-AABD-034F9132951B>.<1> finished with error [40] Error Domain=NSPOSIXErrorDomain Code=40 "Message too long" UserInfo={NSDescription=Message too long, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalWebSocketTask <8990A27D-B6AF-4416-AABD-034F9132951B>.<1>"
), _NSURLErrorFailingURLSessionTaskErrorKey=LocalWebSocketTask <8990A27D-B6AF-4416-AABD-034F9132951B>.<1>}
❌ WebSocket错误: Error Domain=NSPOSIXErrorDomain Code=40 "Message too long" UserInfo={NSDescription=Message too long}
⚠️ TTS启动超时，继续监听完成状态
🎵 开始监听TTS播放完成
💔 心跳检测：连接已断开
🔄 尝试重新连接 (1/3)
🧹 已清理WebSocket连接资源
❌ 文本合成超时
❌ 文本合成超时
❌ 文本合成超时
❌ 文本合成超时
❌ 文本合成超时
❌ 文本合成超时
⚠️ 片段 3 合成返回空数据，重试 1/2
⚠️ 片段 2 合成返回空数据，重试 1/2
⚠️ 片段 5 合成返回空数据，重试 1/2
⚠️ 片段 6 合成返回空数据，重试 1/2
⚠️ 片段 4 合成返回空数据，重试 1/2
⚠️ 片段 1 合成返回空数据，重试 1/2
🎤 合成文本到数据: 要不要...下次一起去呀？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 5959032E-322D-42C6-806E-D4866D3B232F
🎤 合成文本到数据: 对了！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: F4233D09-4469-45C1-A566-A694144459F3
🔗 建立新的WebSocket连接
⏳ 等待其他连接任务完成...
🎤 合成文本到数据: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎤 合成文本到数据: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下......
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎤 合成文本到数据: 啊！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎤 合成文本到数据: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=13字符, 字节=33
📝 文本内容: 要不要...下次一起去呀？...
📦 创建消息: 大小=440字节, Payload=432字节, 压缩=无
📦 创建消息: 大小=440字节, Payload=432字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=42字符, 字节=126
📝 文本内容: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！...
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=45字符, 字节=120
📝 文本内容: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 啊！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=23字符, 字节=65
📝 文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=23字符, 字节=65
📝 文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: 5959032E-322D-42C6-806E-D4866D3B232F
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 要不要...下次一起去呀？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 啊！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=3字符, 字节=9
📝 文本内容: 对了！...
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: 69D9829C-9B2B-4CF9-87AD-A724EC7DC241
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: F4233D09-4469-45C1-A566-A694144459F3
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 对了！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -22
🎵 收到音频数据: 215662 字节，总计: 215662 字节
🎵 开始流式播放 (缓冲: 215662 字节)
🎵 开始流式播放，当前数据: 215662 字节
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 215662 字节
🔊 流式播放开始 (音色: zh_female_cancan_mars_bigtts, 情感: 开心)，当前时长: 4.492041666666666 秒
🎵 音频数据接收完成 (sequence=-22, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 215662 字节
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
✅ 片段 3 合成完成，音频大小: 215662 字节
🎵 音频数据详情: 片段=要不要...下次一起去呀？..., 大小=215662字节, reqid=5959032E-322D-42C6-806E-D4866D3B232F
⚠️ 片段 5 合成返回空数据，重试 2/2
⚠️ 片段 2 合成返回空数据，重试 2/2
⚠️ 片段 6 合成返回空数据，重试 2/2
⚠️ 片段 4 合成返回空数据，重试 2/2
⚠️ 片段 1 合成返回空数据，重试 2/2
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -9
🎵 收到音频数据: 51584 字节，总计: 51584 字节
🎵 音频数据接收完成 (sequence=-9, flag=3)，流式播放模式
🎤 合成文本到数据: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下......
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎤 合成文本到数据: 对了！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: F4233D09-4469-45C1-A566-A694144459F3
🎤 合成文本到数据: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎤 合成文本到数据: 啊！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎤 合成文本到数据: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=45字符, 字节=120
📝 文本内容: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=3字符, 字节=9
📝 文本内容: 对了！...
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
📦 创建消息: 大小=416字节, Payload=408字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=42字符, 字节=126
📝 文本内容: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！...
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
📦 创建消息: 大小=533字节, Payload=525字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 啊！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=23字符, 字节=65
📝 文本内容: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！...
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📦 创建消息: 大小=472字节, Payload=464字节, 压缩=无
📤 发送TTS请求: DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: （突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: F4233D09-4469-45C1-A566-A694144459F3
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 对了！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 32D1ACD5-ED55-4EA1-B09D-9584B967278F
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: （开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 99938A97-DD16-475B-AC44-B59EFAF30B56
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 啊！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: A67D3EA2-8CB5-4BBF-8775-82DF355067DB
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 15
❌ TTS服务错误 (3031): {"reqid":"DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1","message":"[operation=query sequence=-30 code=3000 data_length=337690] Received incorrect response ","code":500,"backend_code":0}
📥 收到消息类型: 15
❌ TTS服务错误 (3031): {"reqid":"DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1","message":"[operation=query sequence=-30 code=3000 data_length=337690] Received incorrect response ","code":500,"backend_code":0}
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -10
🎵 收到音频数据: 52006 字节，总计: 52006 字节
🎵 音频数据接收完成 (sequence=-10, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 52006 字节
✅ 文本合成完成，音频数据大小: 52006 字节
✅ 文本合成完成，音频数据大小: 51584 字节
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
✅ 片段 6 合成完成，音频大小: 52006 字节
🎵 音频数据详情: 片段=（突然想起什么）你叫我宝宝的时候，心跳突..., 大小=52006字节, reqid=DE1010C5-7471-4D8F-A1F4-D663D9ED2FD1
✅ 片段 5 合成完成，音频大小: 51584 字节
🎵 音频数据详情: 片段=对了！..., 大小=51584字节, reqid=F4233D09-4469-45C1-A566-A694144459F3
⚠️ 片段 2 合成返回空数据，重试 3/2
❌ 片段 2 合成最终失败，已重试 2 次
⚠️ 片段 4 合成返回空数据，重试 3/2
❌ 片段 4 合成最终失败，已重试 2 次
⚠️ 片段 1 合成返回空数据，重试 3/2
❌ 片段 1 合成最终失败，已重试 2 次
🎵 所有情感片段合成请求已发送，开始按顺序播放...
🔄 等待片段 1 合成完成...
⚠️ 片段 1 合成失败，跳过播放
🔄 等待片段 2 合成完成...
⚠️ 片段 2 合成失败，跳过播放
🔄 等待片段 3 合成完成...
🔊 开始播放片段 3: 要不要...下次一起去呀？...
📊 TTS播放进度: 3/6
📊 TTS播放进度: 3/6
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -35
🎵 收到音频数据: 414210 字节，总计: 414210 字节
🎵 音频数据接收完成 (sequence=-35, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 414210 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -11
🎵 收到音频数据: 57654 字节，总计: 471864 字节
🎵 音频数据接收完成 (sequence=-11, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 471864 字节
✅ TTS音频播放完成 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)
🔄 TTS播放状态已重置，准备接受新的语音输入
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -19
🎵 收到音频数据: 215092 字节，总计: 215092 字节
🎵 开始流式播放 (缓冲: 215092 字节)
🎵 开始流式播放，当前数据: 215092 字节
🔊 流式播放开始 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)，当前时长: 4.480166666666666 秒
🎵 音频数据接收完成 (sequence=-19, flag=3)，流式播放模式
✅ 片段 3 播放完成
✅ 片段 3 播放完成
🔄 等待片段 4 合成完成...
⚠️ 片段 4 合成失败，跳过播放
🔄 等待片段 5 合成完成...
🔊 开始播放片段 5: 对了！...
📊 TTS播放进度: 5/6
📊 TTS播放进度: 5/6
✅ 片段 5 播放完成
✅ 片段 5 播放完成
🔄 等待片段 6 合成完成...
🔊 开始播放片段 6: （突然想起什么）你叫我宝宝的时候，心跳突...
📊 TTS播放进度: 6/6
📊 TTS播放进度: 6/6
✅ 片段 6 播放完成
✅ 片段 6 播放完成
✅ 所有情感片段处理完成
🎉 流式TTS播放完成
🎵 收到TTS播放完成通知
📢 已发送TTS播放完成通知
🔄 重新开始语音监听
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
✅ TTS音频播放完成 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)
🔄 TTS播放状态已重置，准备接受新的语音输入
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
💓 心跳正常
Task <A059A171-8899-45ED-A527-C6213D65B937>.<2> finished with error [-1001] Error Domain=NSURLErrorDomain Code=-1001 "The request timed out." UserInfo={_kCFStreamErrorCodeKey=-2103, _NSURLErrorFailingURLSessionTaskErrorKey=LocalWebSocketTask <A059A171-8899-45ED-A527-C6213D65B937>.<2>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalWebSocketTask <A059A171-8899-45ED-A527-C6213D65B937>.<2>"
), NSLocalizedDescription=The request timed out., NSErrorFailingURLStringKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, NSErrorFailingURLKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, _kCFStreamErrorDomainKey=4}
Task <D7E0870E-A0D6-4B34-9B36-1ED2D7B90DEB>.<3> finished with error [-1001] Error Domain=NSURLErrorDomain Code=-1001 "The request timed out." UserInfo={_kCFStreamErrorCodeKey=-2103, _NSURLErrorFailingURLSessionTaskErrorKey=LocalWebSocketTask <D7E0870E-A0D6-4B34-9B36-1ED2D7B90DEB>.<3>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalWebSocketTask <D7E0870E-A0D6-4B34-9B36-1ED2D7B90DEB>.<3>"
), NSLocalizedDescription=The request timed out., NSErrorFailingURLStringKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, NSErrorFailingURLKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, _kCFStreamErrorDomainKey=4}
💓 心跳正常
💔 心跳检测：连接已断开
🔄 尝试重新连接 (1/3)
🧹 已清理WebSocket连接资源