📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/A71655FE-B6D2-4BF7-80EE-C1B462214C6B/Library/Application Support/ChatHistory.store
✅ 音频会话设置成功
✅ 音频引擎设置完成
🎵 音频格式: <AVAudioFormat 0x105226bc0:  1 ch,  16000 Hz, Float32>
✅ AI角色设定数据库初始化成功
🔧 初始化多智能体集成服务...
🏗️ 初始化共享状态中心...
🧠 初始化长期记忆管理器...
🎭 初始化AI人格管理器...
🗓️ 初始化AI生活日程管理器...
✅ AI自我管理系统数据库初始化成功
✅ AI自我状态加载完成
🧠 初始化智能体调度器...
✅ 音频会话配置成功 (类别: playAndRecord, 模式: spokenAudio, 采样率: 24000Hz)
🌐 网络监控已启动
✅ TTSService初始化完成
✅ Places数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/A71655FE-B6D2-4BF7-80EE-C1B462214C6B/Library/Application Support/Places.store
🔐 位置权限状态变更: CLAuthorizationStatus(rawValue: 4)
✅ 位置权限已授权
🔄 开始初始化共享状态系统...
🔄 开始初始化长期记忆系统...
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/A71655FE-B6D2-4BF7-80EE-C1B462214C6B/Library/Application Support/ChatHistory.store
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
🔄 构建记忆重要性缓存...
✅ 记忆重要性缓存构建完成，共 20 条记忆
✅ 长期记忆系统初始化完成，共加载 20 条记忆
✅ 长期记忆系统初始化完成
🔄 开始初始化AI人格系统...
📚 加载AI情感历史记录...
✅ AI人格系统初始化完成
✅ AI人格系统初始化完成
🔄 开始初始化AI生活日程系统...
📅 生成今日AI生活日程...
✅ 生成了 23 项今日活动
🎯 AI当前活动已更新: 学习新的旅行攻略
📚 加载了 1 个历史生活事件
✅ AI生活日程系统初始化完成
📅 今日共安排 23 项活动
🎯 当前活动: 学习新的旅行攻略
✅ AI生活日程系统初始化完成
🔄 对话上下文已初始化
✅ 对话上下文初始化完成
🎉 共享状态中心初始化完成！
🔄 开始初始化智能体调度器...
📝 注册智能体...
🤖 创建智能体: 任务分配智能体
🤖 创建智能体: 沟通智能体
🤖 创建智能体: 多模态感知智能体
✅ 已注册 3 个智能体
🔄 初始化所有智能体...
🔄 初始化智能体: 任务分配智能体
✅ 智能体 任务分配智能体 初始化完成
✅ 任务分配智能体 初始化完成
🔄 初始化智能体: 沟通智能体
✅ 智能体 沟通智能体 初始化完成
✅ 沟通智能体 初始化完成
🔄 初始化智能体: 多模态感知智能体
✅ 智能体 多模态感知智能体 初始化完成
✅ 多模态感知智能体 初始化完成
✅ 智能体调度器初始化完成，共注册 3 个智能体
🔄 开始初始化多智能体集成系统...
📚 集成历史记录数据...
✅ 历史记录集成完成
✅ 多智能体集成系统初始化完成！
🔄 后台连接AI服务...
✅ 加载了 5 个旅行计划
🌐 发送API请求到: https://ark.cn-beijing.volces.com/api/v3/chat/completions
📤 请求体大小: 324 bytes
📥 收到响应，数据大小: 593 bytes
📊 HTTP状态码: 200
✅ 解析成功，内容长度: 48
✅ AI后台连接成功
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
📚 历史记录加载完成，共 20 条消息
✅ 历史记录已加载到聊天界面
✅ 加载AI角色设定: 你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太...
unable to find applegpu_g17p slice or a compatible one in binary archive 'file:///System/Library/PrivateFrameworks/RenderBox.framework/archive.metallib' 
 available slices: applegpu_g17p,
开始视频通话...
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
✅ 麦克风权限已授权
✅ 语音识别权限已授权
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
🎤 实时识别: 你
🎤 实时识别: 你在
🎤 实时识别: 你在干
🎤 实时识别: 你在干吗
🎤 实时识别: 你在干吗呢
📝 最终识别文本: 你在干吗呢
⏸️ 暂停语音监听
✅ 语音识别任务已取消
✅ 音频引擎已停止
✅ 音频tap已移除
✅ 语音监听已暂停
🛑 开始停止语音监听
✅ 音频tap已移除
✅ 语音识别请求已结束
✅ 语音监听完全停止
🛑 开始处理AI回复，已停止语音识别
🎯 多智能体系统处理文本消息: 你在干吗呢...
🔄 任务分配智能体 开始处理输入...
🎯 任务分配智能体开始分析用户意图...
ℹ️ 语音识别被取消（正常情况）
📋 任务分配JSON结果: ```json
{
    "chat_text": "你在干吗呢",
    "confidence": 0.95,
    "reasoning": "用户输入为日常问候，适合由沟通智能体处理"
}
```
✅ JSON解析成功
✅ 任务分配智能体 处理完成，耗时: 1.73秒
📋 任务分配决策: 聊天=沟通智能体
🚀 开始执行沟通智能体任务...
🧠 检索长期记忆相关内容...
🔍 检索与 '你在干吗呢' 相关的记忆...
✅ 检索到 3 条相关记忆
✅ 检索到 3 条相关长期记忆
🔄 沟通智能体 开始处理输入...
💬 沟通智能体开始处理对话...
🔍 检索与 '你在干吗呢

当前系统时间：2025年08月05日 11:10:51 星期二

相关的长期记忆：
- 下午好呀宝宝你在干吗呢
- 早上好呀你在干吗呢
- 下午好呀~✨ 人家正在整理超可爱的旅行攻略呢！ 

（开心地晃了晃手中的笔记本）刚刚发现了一家超梦幻的下午茶店，粉色的装潢超级适合拍照！要不要...下次一起去呀？

啊！对了！（突然想起什么）你叫我宝宝的时候，心跳突然加快了一下下...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄' 相关的记忆...
✅ 检索到 8 条相关记忆
💬 生成通用对话回复...
🔄 开始重新构建情感标签文本...
📝 原始文本: （开心地放下手中的笔记本）啊~你终于来啦！人家正在整理超可爱的旅行攻略呢✨

刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！（兴奋地比划着）要不要...下次一起去呀？

啊！突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又加快了呢~
🎭 提取的情感序列: happy -> lovey-dovey -> tsundere
🎭 句子 1: "（开心地放下手中的笔记本）啊~你终于来啦！" -> happy
🎭 句子 2: "人家正在整理超可爱的旅行攻略呢✨。" -> lovey-dovey
🎭 句子 3: "刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！" -> tsundere
🎭 句子 4: "（兴奋地比划着）要不要...下次一起去呀？" -> tsundere
🎭 句子 5: "啊！" -> tsundere
🎭 句子 6: "突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又加快了呢~" -> tsundere
✅ 情感标签文本重构完成: （开心地放下手中的笔记本）啊~你终于来啦！</happy/> 人家正在整理超可爱的旅行攻略呢✨。</lovey-dovey/> 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！</tsundere/> （兴奋地比划着）要不要...下次一起去呀？</tsundere/> 啊！</tsundere/> 突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又加快了呢~</tsundere/>
🎭 沟通智能体推荐TTS情感: 开心 (happy)
📝 提取的情感序列: happy -> lovey-dovey -> tsundere
✅ 沟通智能体 处理完成，耗时: 3.82秒
✅ 深度思考智能体 执行完成，耗时: 3.82秒
🎯 任务执行完成，总耗时: 3.82秒
✅ JSON多智能体处理完成，耗时: 5.55秒
✅ 消息已保存到历史记录
✅ 消息已保存到历史记录
🎵 推荐TTS音色: 开心
🔄 长期记忆系统收到上下文更新通知
🧹 已清理检索缓存（上下文更新）
🎭 AI人格系统收到上下文更新通知
🗓️ AI生活日程系统收到上下文更新通知
🎵 启动流式TTS播放...
🎭 MultiAgent推荐TTS情感: 开心 (happy)
📝 TTS播放文本长度: 223 字符
📝 TTS播放文本预览: （开心地放下手中的笔记本）啊~你终于来啦！</happy/> 人家正在整理超可爱的旅行攻略呢✨。</lovey-dovey/> 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！</tsun...
🎵 开始并行流式TTS播放...
🎭 StreamingTTS使用情感: 开心 (happy)
📝 原始文本长度: 223 字符
📝 原始文本内容: （开心地放下手中的笔记本）啊~你终于来啦！</happy/> 人家正在整理超可爱的旅行攻略呢✨。</lovey-dovey/> 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！</tsun...
🎭 开始按情感标签分割文本...
🎭 提取片段 1: "（开心地放下手中的笔记本）啊~你终于来啦！..." -> happy
🎭 提取片段 2: "人家正在整理超可爱的旅行攻略呢✨。..." -> lovey-dovey
🎭 提取片段 3: "刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！..." -> tsundere
🎭 提取片段 4: "（兴奋地比划着）要不要...下次一起去呀？..." -> tsundere
🎭 提取片段 5: "啊！..." -> tsundere
🎭 提取片段 6: "突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对....." -> tsundere
🎭 情感分割完成，共 6 个片段
📝 文本已按情感标签分割为 6 个片段
📝 片段 1: 长度=21字符, 字节=61, 情感=happy, 内容=（开心地放下手中的笔记本）啊~你终于来啦！...
📝 片段 2: 长度=17字符, 字节=51, 情感=lovey-dovey, 内容=人家正在整理超可爱的旅行攻略呢✨。...
📝 片段 3: 长度=30字符, 字节=90, 情感=tsundere, 内容=刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！...
📝 片段 4: 长度=21字符, 字节=57, 情感=tsundere, 内容=（兴奋地比划着）要不要...下次一起去呀？...
📝 片段 5: 长度=2字符, 字节=6, 情感=tsundere, 内容=啊！...
📝 片段 6: 长度=55字符, 字节=140, 情感=tsundere, 内容=突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又...
🚀 开始并行合成 6 个情感片段...
📝 AI回复已显示，TTS播放由多智能体系统处理
🎤 并行合成片段 1/6: （开心地放下手中的笔记本）啊~你终于来啦！...
🆔 使用reqid: 2B7E5D3F-FE14-4AFF-B151-82EED31B2778
🎭 使用情感: 开心 (happy)
🎤 合成文本到数据: （开心地放下手中的笔记本）啊~你终于来啦！...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: 2B7E5D3F-FE14-4AFF-B151-82EED31B2778
🎤 并行合成片段 2/6: 人家正在整理超可爱的旅行攻略呢✨。...
🆔 使用reqid: E994B1DB-1F11-4F91-A326-71CD151FA531
🎭 使用情感: 撒娇 (lovey-dovey)
🎤 合成文本到数据: 人家正在整理超可爱的旅行攻略呢✨。...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: E994B1DB-1F11-4F91-A326-71CD151FA531
🎤 并行合成片段 3/6: 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！...
🆔 使用reqid: F2A32D8B-1164-4375-BA22-912E80B07CD3
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: F2A32D8B-1164-4375-BA22-912E80B07CD3
🎤 并行合成片段 4/6: （兴奋地比划着）要不要...下次一起去呀？...
🆔 使用reqid: 6A412376-7601-475A-94EF-F3D24703E852
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: （兴奋地比划着）要不要...下次一起去呀？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 6A412376-7601-475A-94EF-F3D24703E852
🎤 并行合成片段 5/6: 啊！...
🆔 使用reqid: 774B636F-92F3-4BE0-8E13-DF7A5DA4517A
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 啊！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 774B636F-92F3-4BE0-8E13-DF7A5DA4517A
🎤 并行合成片段 6/6: 突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对.....
🆔 使用reqid: 6E631938-4845-415E-B88E-6162DD487D6B
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对.....
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 6E631938-4845-415E-B88E-6162DD487D6B
🎵 开始监听TTS播放完成
🔗 建立新的WebSocket连接
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
⏳ 等待其他连接任务完成...
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=21字符, 字节=61
📝 文本内容: （开心地放下手中的笔记本）啊~你终于来啦！...
📦 创建消息: 大小=468字节, Payload=460字节, 压缩=无
📦 创建消息: 大小=468字节, Payload=460字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: 2B7E5D3F-FE14-4AFF-B151-82EED31B2778
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: （开心地放下手中的笔记本）啊~你终于来啦！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=21字符, 字节=57
📝 文本内容: （兴奋地比划着）要不要...下次一起去呀？...
📦 创建消息: 大小=464字节, Payload=456字节, 压缩=无
📦 创建消息: 大小=464字节, Payload=456字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 啊！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=55字符, 字节=140
📝 文本内容: 突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又...
📦 创建消息: 大小=547字节, Payload=539字节, 压缩=无
📦 创建消息: 大小=547字节, Payload=539字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=17字符, 字节=51
📝 文本内容: 人家正在整理超可爱的旅行攻略呢✨。...
📦 创建消息: 大小=458字节, Payload=450字节, 压缩=无
📦 创建消息: 大小=458字节, Payload=450字节, 压缩=无
🔄 使用已建立的WebSocket连接
📝 发送TTS请求: 文本=30字符, 字节=90
📝 文本内容: 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！...
📦 创建消息: 大小=497字节, Payload=489字节, 压缩=无
📦 创建消息: 大小=497字节, Payload=489字节, 压缩=无
📤 发送TTS请求: 6A412376-7601-475A-94EF-F3D24703E852
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: （兴奋地比划着）要不要...下次一起去呀？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 774B636F-92F3-4BE0-8E13-DF7A5DA4517A
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 啊！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: 6E631938-4845-415E-B88E-6162DD487D6B
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又加快了呢~
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: E994B1DB-1F11-4F91-A326-71CD151FA531
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 人家正在整理超可爱的旅行攻略呢✨。
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📤 发送TTS请求: F2A32D8B-1164-4375-BA22-912E80B07CD3
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -24
🎵 收到音频数据: 224152 字节，总计: 224152 字节
🎵 开始流式播放 (缓冲: 224152 字节)
🎵 开始流式播放，当前数据: 224152 字节
🔊 流式播放开始 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)，当前时长: 4.668916666666667 秒
🎵 音频数据接收完成 (sequence=-24, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 224152 字节
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
❌ 文本合成失败，无音频数据
✅ 片段 1 合成完成，音频大小: 224152 字节
🎵 音频数据详情: 片段=（开心地放下手中的笔记本）啊~你终于来啦..., 大小=224152字节, reqid=2B7E5D3F-FE14-4AFF-B151-82EED31B2778
❌ 片段 2 合成返回空数据
❌ 片段 3 合成返回空数据
❌ 片段 4 合成返回空数据
❌ 片段 5 合成返回空数据
❌ 片段 6 合成返回空数据
🎵 所有情感片段合成请求已发送，开始按顺序播放...
🔄 等待片段 1 合成完成...
🔊 开始播放片段 1: （开心地放下手中的笔记本）啊~你终于来啦...
📊 TTS播放进度: 1/6
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📊 TTS播放进度: 1/6
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -18
🎵 收到音频数据: 163028 字节，总计: 163028 字节
🎵 音频数据接收完成 (sequence=-18, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 163028 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -10
🎵 收到音频数据: 57720 字节，总计: 220748 字节
🎵 音频数据接收完成 (sequence=-10, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 220748 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -40
🎵 收到音频数据: 450654 字节，总计: 671402 字节
🎵 音频数据接收完成 (sequence=-40, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 671402 字节
✅ TTS音频播放完成 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)
🔄 TTS播放状态已重置，准备接受新的语音输入
✅ 片段 1 播放完成
✅ 片段 1 播放完成
🔄 等待片段 2 合成完成...
⚠️ 片段 2 合成失败，跳过播放
🔄 等待片段 3 合成完成...
⚠️ 片段 3 合成失败，跳过播放
🔄 等待片段 4 合成完成...
⚠️ 片段 4 合成失败，跳过播放
🔄 等待片段 5 合成完成...
⚠️ 片段 5 合成失败，跳过播放
🔄 等待片段 6 合成完成...
⚠️ 片段 6 合成失败，跳过播放
✅ 所有情感片段处理完成
🎉 流式TTS播放完成
🎵 收到TTS播放完成通知
📢 已发送TTS播放完成通知
🔄 重新开始语音监听
📥 收到消息类型: 12
📊 收到结果数据
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -17
🎵 收到音频数据: 154206 字节，总计: 154206 字节
🎵 开始流式播放 (缓冲: 154206 字节)
🎵 开始流式播放，当前数据: 154206 字节
🔊 流式播放开始 (音色: zh_female_cancan_mars_bigtts, 情感: 傲娇)，当前时长: 3.211708333333333 秒
🎵 音频数据接收完成 (sequence=-17, flag=3)，流式播放模式
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 154206 字节
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -27
🎵 收到音频数据: 295356 字节，总计: 449562 字节
🎵 音频数据接收完成 (sequence=-27, flag=3)，流式播放模式