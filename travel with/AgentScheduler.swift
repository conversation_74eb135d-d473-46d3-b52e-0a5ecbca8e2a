//
//  AgentScheduler.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import UIKit

// MARK: - 智能体调度器
/// 多智能体系统的核心调度器，负责分析用户意图、制定执行计划、协调智能体协同工作
@MainActor
class AgentScheduler: ObservableObject {
    
    // MARK: - 属性
    
    /// 共享状态中心
    private let sharedState: SharedStateHub
    
    /// 注册的智能体集合
    internal var agents: [AgentIdentifier: Agent] = [:]
    
    /// API配置
    private let apiKey = "5bd19fd1-fee3-4290-b8d8-f21fcadeaf51"
    private let baseURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    private let urlSession: URLSession
    
    /// 调度器状态
    @Published var isReady: Bool = false
    @Published var isProcessing: Bool = false
    @Published var lastExecutionTime: TimeInterval = 0
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub) {
        self.sharedState = sharedState
        
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60
        config.timeoutIntervalForResource = 120
        self.urlSession = URLSession(configuration: config)
        
        print("🧠 初始化智能体调度器...")
        
        // 启动初始化流程
        Task {
            await initialize()
        }
    }
    
    /// 初始化调度器和所有智能体
    private func initialize() async {
        print("🔄 开始初始化智能体调度器...")
        
        // 等待共享状态中心就绪
        while !sharedState.isSystemReady() {
            try? await Task.sleep(nanoseconds: 100_000_000) // 等待100ms
        }
        
        // 注册所有智能体
        await registerAllAgents()
        
        // 初始化所有智能体
        await initializeAllAgents()
        
        isReady = true
        print("✅ 智能体调度器初始化完成，共注册 \(agents.count) 个智能体")
    }
    
    /// 注册所有智能体
    private func registerAllAgents() async {
        print("📝 注册智能体...")
        
        // 创建并注册各个智能体
        agents[AgentIdentifier.taskAssignment] = TaskAssignmentAgent(sharedState: sharedState, scheduler: self)
        agents[AgentIdentifier.deepThinking] = DeepThinkingAgent(sharedState: sharedState, scheduler: self)
        agents[AgentIdentifier.multimodal] = MultimodalAgent(sharedState: sharedState, scheduler: self)
        
        print("✅ 已注册 \(agents.count) 个智能体")
    }
    
    /// 初始化所有智能体
    private func initializeAllAgents() async {
        print("🔄 初始化所有智能体...")
        
        for (id, agent) in agents {
            await agent.initialize()
            print("✅ \(id.displayName) 初始化完成")
        }
    }
    
    // MARK: - 主要调度方法
    
    /// 处理用户输入的主要入口点
    /// - Parameter userInput: 用户输入数据
    /// - Returns: AI响应结果
    func processUserInput(_ userInput: UserInput) async -> AIResponse {
        guard isReady else {
            print("⚠️ 调度器未就绪，返回默认响应")
            return AIResponse(content: "系统正在初始化中，请稍后再试...", processingTime: 0)
        }
        
        isProcessing = true
        let startTime = Date()
        
        print("🎯 开始处理用户输入: \(userInput.message.prefix(50))...")
        
        do {
            // 1. 分析用户意图
            let intent = await analyzeUserIntent(userInput)
            print("🔍 用户意图分析结果: \(intent)")
            
            // 2. 制定执行计划
            let plan = await createExecutionPlan(for: intent, input: userInput)
            print("📋 执行计划: 主要智能体=\(plan.primaryAgent.displayName), 支持智能体=\(plan.supportingAgents.count)个")
            
            // 3. 执行智能体计划
            let result = await executeAgentPlan(plan, input: userInput)
            
            // 4. 整合最终响应
            let response = await integrateResponse(result, originalInput: userInput)
            
            let processingTime = Date().timeIntervalSince(startTime)
            lastExecutionTime = processingTime
            
            print("✅ 用户输入处理完成，耗时: \(String(format: "%.2f", processingTime))秒")
            
            return response
            
        } catch {
            print("❌ 处理用户输入时发生错误: \(error)")
            
            let processingTime = Date().timeIntervalSince(startTime)
            return AIResponse(
                content: "抱歉，我现在有点忙不过来，请稍后再试试吧 😅",
                processingTime: processingTime,
                error: error
            )
        }
        
        isProcessing = false
    }
    
    // MARK: - 意图分析
    
    /// 分析用户意图
    private func analyzeUserIntent(_ userInput: UserInput) async -> UserIntent {
        guard let taskAgent = agents[.taskAssignment] else {
            print("⚠️ 任务分配智能体不可用，使用默认意图分析")
            return analyzeIntentLocally(userInput.message)
        }
        
        let agentInput = AgentInput(
            userMessage: userInput.message,
            messageType: userInput.messageType,
            imageData: userInput.imageData,
            context: sharedState.currentContext
        )
        
        let output = await taskAgent.process(agentInput)
        
        // 从智能体输出中解析意图
        return parseIntentFromOutput(output.content)
    }
    
    /// 本地意图分析（备用方案）
    private func analyzeIntentLocally(_ message: String) -> UserIntent {
        let lowercased = message.lowercased()
        
        if lowercased.contains("图片") || lowercased.contains("照片") || lowercased.contains("看看") {
            return .imageQuery
        } else if lowercased.contains("难过") || lowercased.contains("伤心") || lowercased.contains("心情") {
            return .emotionalSupport
        } else if lowercased.contains("旅行") || lowercased.contains("攻略") || lowercased.contains("规划") {
            return .travelPlanning
        } else if lowercased.contains("为什么") || lowercased.contains("怎么") || lowercased.contains("复杂") {
            return .deepChat
        } else {
            return .simpleChat
        }
    }
    
    /// 从智能体输出解析意图
    private func parseIntentFromOutput(_ output: String) -> UserIntent {
        let lowercased = output.lowercased()
        
        if lowercased.contains("simple_chat") {
            return .simpleChat
        } else if lowercased.contains("deep_chat") {
            return .deepChat
        } else if lowercased.contains("image_query") {
            return .imageQuery
        } else if lowercased.contains("emotional_support") {
            return .emotionalSupport
        } else if lowercased.contains("travel_planning") {
            return .travelPlanning
        } else {
            return .simpleChat
        }
    }
    
    // MARK: - 执行计划制定
    
    /// 制定智能体执行计划
    private func createExecutionPlan(for intent: UserIntent, input: UserInput) async -> AgentExecutionPlan {
        switch intent {
        case .simpleChat:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.deepThinking,  // 使用统一的沟通智能体
                supportingAgents: [], // 不需要支持智能体
                executionOrder: .sequential,
                expectedProcessingTime: 2.0,
                priority: 0.7
            )

        case .deepChat:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.deepThinking,
                supportingAgents: [], // 不需要支持智能体
                executionOrder: .sequential,
                expectedProcessingTime: 8.0,
                priority: 0.9
            )

        case .imageQuery:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.multimodal,
                supportingAgents: [AgentIdentifier.deepThinking], // 使用统一的沟通智能体
                executionOrder: .sequential,
                expectedProcessingTime: 4.0,
                priority: 0.8
            )

        case .emotionalSupport:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.deepThinking,  // 使用沟通智能体处理情感支持
                supportingAgents: [],
                executionOrder: .sequential,
                expectedProcessingTime: 5.0,
                priority: 1.0
            )

        case .travelPlanning:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.deepThinking,
                supportingAgents: [],
                executionOrder: .sequential,
                expectedProcessingTime: 10.0,
                priority: 0.9
            )

        case .unknown:
            return AgentExecutionPlan(
                primaryAgent: AgentIdentifier.deepThinking,  // 使用统一的沟通智能体
                supportingAgents: [],
                executionOrder: .sequential,
                expectedProcessingTime: 3.0,
                priority: 0.5
            )
        }
    }
    
    // MARK: - 计划执行
    
    /// 执行智能体计划
    private func executeAgentPlan(_ plan: AgentExecutionPlan, input: UserInput) async -> AgentExecutionResult {
        var outputs: [AgentOutput] = []
        var errors: [AgentError] = []
        var executedAgents: [AgentIdentifier] = []
        
        let startTime = Date()
        
        // 准备智能体输入
        let agentInput = AgentInput(
            userMessage: input.message,
            messageType: input.messageType,
            imageData: input.imageData,
            context: sharedState.currentContext
        )
        
        // 根据执行顺序决定是并行还是串行
        switch plan.executionOrder {
        case .sequential:
            // 串行执行：先执行主要智能体，再执行支持智能体
            if let primaryAgent = agents[plan.primaryAgent] {
                let output = await primaryAgent.process(agentInput)
                outputs.append(output)
                executedAgents.append(plan.primaryAgent)
                print("✅ 主要智能体 \(plan.primaryAgent.displayName) 执行完成")
            }

            // 串行执行支持智能体
            for agentId in plan.supportingAgents {
                if let agent = agents[agentId] {
                    let output = await agent.process(agentInput)
                    outputs.append(output)
                    executedAgents.append(agentId)
                    print("✅ 支持智能体 \(agentId.displayName) 执行完成")
                }
            }

        case .parallel:
            // 并行执行：主要智能体和支持智能体同时执行
            var tasks: [Task<(AgentOutput, AgentIdentifier), Never>] = []

            // 添加主要智能体任务
            if let primaryAgent = agents[plan.primaryAgent] {
                let task = Task {
                    let output = await primaryAgent.process(agentInput)
                    return (output, plan.primaryAgent)
                }
                tasks.append(task)
            }

            // 添加支持智能体任务
            for agentId in plan.supportingAgents {
                if let agent = agents[agentId] {
                    let task = Task {
                        let output = await agent.process(agentInput)
                        return (output, agentId)
                    }
                    tasks.append(task)
                }
            }

            // 等待所有任务完成
            for task in tasks {
                let (output, agentId) = await task.value
                outputs.append(output)
                executedAgents.append(agentId)
                print("✅ 智能体 \(agentId.displayName) 执行完成")
            }

        case .conditional:
            // 条件执行：根据主要智能体的结果决定是否执行支持智能体
            if let primaryAgent = agents[plan.primaryAgent] {
                let output = await primaryAgent.process(agentInput)
                outputs.append(output)
                executedAgents.append(plan.primaryAgent)
                print("✅ 主要智能体 \(plan.primaryAgent.displayName) 执行完成")

                // 根据主要智能体的置信度决定是否执行支持智能体
                if output.confidence < 0.8 {
                    print("🔄 主要智能体置信度较低，执行支持智能体...")
                    for agentId in plan.supportingAgents {
                        if let agent = agents[agentId] {
                            let supportOutput = await agent.process(agentInput)
                            outputs.append(supportOutput)
                            executedAgents.append(agentId)
                            print("✅ 支持智能体 \(agentId.displayName) 执行完成")
                        }
                    }
                }
            }
        }
        
        let totalTime = Date().timeIntervalSince(startTime)
        
        // 生成临时响应（将在整合阶段进一步处理）
        let primaryOutput = outputs.first?.content ?? "处理完成"
        
        return AgentExecutionResult(
            outputs: outputs,
            finalResponse: primaryOutput,
            totalProcessingTime: totalTime,
            executedAgents: executedAgents,
            errors: errors
        )
    }
    
    // MARK: - 响应整合
    
    /// 整合最终响应
    private func integrateResponse(_ result: AgentExecutionResult, originalInput: UserInput) async -> AIResponse {
        // 过滤掉空内容的输出，只保留有实际内容的输出
        let validOutputs = result.outputs.filter { !$0.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        // 获取主要输出（第一个有内容的输出）
        guard let primaryOutput = validOutputs.first else {
            return AIResponse(content: "抱歉，处理过程中出现了问题", processingTime: result.totalProcessingTime)
        }

        // 更新AI状态
        if let emotionRecommendation = primaryOutput.emotionRecommendation {
            sharedState.updateAIEmotion(emotionRecommendation)
        }

        // 更新对话上下文
        sharedState.updateConversationContext(userMessage: originalInput.message, aiResponse: primaryOutput.content)

        // 构建最终响应
        return AIResponse(
            content: primaryOutput.content,
            processingTime: result.totalProcessingTime,
            confidence: primaryOutput.confidence,
            ttsEmotion: primaryOutput.ttsEmotion,
            executedAgents: result.executedAgents,
            metadata: primaryOutput.metadata
        )
    }
    
    // MARK: - API调用方法
    
    /// 调用大模型API
    func callAPI(with agentId: AgentIdentifier, messages: [APIMessage]) async -> String {
        guard let url = URL(string: baseURL) else {
            return "URL错误"
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        let requestBody = APIRequest(model: agentId.rawValue, messages: messages)
        
        do {
            let encoder = JSONEncoder()
            request.httpBody = try encoder.encode(requestBody)
            
            let (data, response) = try await urlSession.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                let errorBody = String(data: data, encoding: .utf8) ?? "无法解析错误信息"
                print("❌ API请求失败: \(response) \nBody: \(errorBody)")
                return "请求失败: \((response as? HTTPURLResponse)?.statusCode ?? -1)"
            }
            
            let apiResponse = try JSONDecoder().decode(SchedulerAPIResponse.self, from: data)
            return apiResponse.choices.first?.message.content ?? "没有收到有效回复"
            
        } catch {
            print("❌ API调用错误: \(error)")
            return "请求错误: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 状态查询
    
    /// 获取调度器状态摘要
    func getStatusSummary() -> String {
        let status = isReady ? "✅ 就绪" : "⏳ 初始化中"
        let processing = isProcessing ? "🔄 处理中" : "💤 空闲"
        let agentCount = agents.count
        let lastTime = String(format: "%.2f", lastExecutionTime)
        
        return """
        调度器状态: \(status)
        当前状态: \(processing)
        注册智能体: \(agentCount) 个
        上次执行时间: \(lastTime)秒
        """
    }
}

// MARK: - 数据模型

/// 用户输入数据
struct UserInput {
    let message: String
    let messageType: MessageType
    let imageData: Data?
    let timestamp: Date
    
    init(message: String, messageType: MessageType = .text, imageData: Data? = nil) {
        self.message = message
        self.messageType = messageType
        self.imageData = imageData
        self.timestamp = Date()
    }
}

/// AI响应数据
struct AIResponse {
    let content: String
    let processingTime: TimeInterval
    let confidence: Double
    let ttsEmotion: CanCanEmotion?
    let executedAgents: [AgentIdentifier]
    let metadata: [String: Any]
    let error: Error?
    
    init(content: String, 
         processingTime: TimeInterval, 
         confidence: Double = 1.0, 
         ttsEmotion: CanCanEmotion? = nil, 
         executedAgents: [AgentIdentifier] = [], 
         metadata: [String: Any] = [:], 
         error: Error? = nil) {
        self.content = content
        self.processingTime = processingTime
        self.confidence = confidence
        self.ttsEmotion = ttsEmotion
        self.executedAgents = executedAgents
        self.metadata = metadata
        self.error = error
    }
}

// MARK: - API相关模型

/// API消息
struct APIMessage: Codable {
    let role: String
    let content: String
}

/// API请求
struct APIRequest: Codable {
    let model: String
    let messages: [APIMessage]
}

/// 智能体调度器专用的API响应模型
private struct SchedulerAPIResponse: Codable {
    let choices: [Choice]

    struct Choice: Codable {
        let message: Message

        struct Message: Codable {
            let content: String
        }
    }
}
