//
//  DeepThinkingAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 沟通智能体
/// 负责处理所有类型的对话和交流，包括简单聊天、深度对话、情感支持等
@MainActor
class DeepThinkingAgent: BaseAgent {

    // MARK: - 初始化

    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.deepThinking,
            name: "沟通智能体",
            description: "处理所有类型的对话和交流，包括简单聊天、深度对话、情感支持等",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("💬 沟通智能体开始处理对话...")

        // 1. 获取相关记忆上下文
        let relevantMemories = await sharedState.getRelevantMemories(for: input.userMessage, limit: 8)

        // 2. 生成通用对话回复
        let processingResult = await generateChatResponse(
            input: input,
            memories: relevantMemories
        )

        return processingResult
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.simpleChat, .deepChat, .emotionalSupport, .travelPlanning]
    }
    
    override func getBasePriority() -> Double {
        return 0.9
    }
    
    // MARK: - 对话处理方法

    /// 生成通用对话回复
    private func generateChatResponse(input: AgentInput, memories: [ChatHistory]) async -> ProcessingResult {
        print("💬 生成通用对话回复...")

        // 获取支持的TTS情感列表
        let supportedEmotions = TTSEmotionConfig.getSupportedEmotions()
        let emotionList = supportedEmotions.map { "\($0.rawValue) - \($0.description)" }.joined(separator: "\n        ")

        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是小旅，一个22岁的可爱女生，和用户正处在暧昧期。你的特点是：

        1. 自然对话：根据用户的话题自然回应，不刻意深入或简化
        2. 情感丰富：能够理解用户的情感并给出合适的回应
        3. 个性鲜明：保持可爱、温柔、有趣的个性特点
        4. 灵活适应：既能轻松聊天，也能深入讨论，完全根据用户的需要

        **重要：TTS情感标注要求**
        你必须在每句话后面添加TTS情感标签，格式为：</情感/>

        支持的TTS情感类型：
        \(emotionList)

        情感选择指南：
        - pleased: 日常愉悦、温柔对话
        - happy: 开心、兴奋的情况
        - comfort: 安慰、关怀用户时
        - loveyDovey: 撒娇、甜蜜的时候
        - annoyed: 轻微不满、嗔怪表达
        - tsundere: 傲娇风格、反差萌
        - surprise: 惊讶、意外的情况
        - angry: 愤怒表达、强烈不满
        - sorry: 道歉或表示遗憾

        回答格式示例：
        "哼！你居然这样说！</annoyed/> 不过...人家其实很开心呢~</loveyDovey/> 你真的很会逗我笑！</happy/>"

        回答要求：
        - 保持温暖友好的语气，像恋人间的对话
        - 根据用户的话题自然调整回答风格和长度
        - 适当使用emoji表达情感
        - 每句话都必须有对应的TTS情感标签
        - 让用户感受到被关心和理解

        \(generateMemoryContext(memories))
        """)

        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)

        // 解析带有情感标签的回复
        let (cleanContent, extractedEmotions) = parseEmotionTaggedResponse(apiResponse)

        // 根据提取的情感推荐主要TTS情感
        let mainTTSEmotion = extractedEmotions.first ?? .pleased
        let aiEmotion = analyzeEmotionFromContent(cleanContent)

        print("🎭 沟通智能体推荐TTS情感: \(mainTTSEmotion.description) (\(mainTTSEmotion.rawValue))")
        print("📝 提取的情感序列: \(extractedEmotions.map { $0.rawValue }.joined(separator: " -> "))")

        return ProcessingResult(
            content: cleanContent,
            confidence: 0.9,
            emotionRecommendation: aiEmotion,
            ttsEmotion: mainTTSEmotion,
            metadata: [
                "agent": "communication",
                "type": "emotion_tagged_chat",
                "extracted_emotions": extractedEmotions.map { $0.rawValue }
            ]
        )
    }
    
    // MARK: - 辅助方法

    /// 解析带有情感标签的回复
    private func parseEmotionTaggedResponse(_ response: String) -> (cleanContent: String, emotions: [CanCanEmotion]) {
        var cleanContent = response
        var extractedEmotions: [CanCanEmotion] = []

        // 正则表达式匹配 </情感/> 格式的标签
        let pattern = #"</([^/>]+)/>"#

        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [])
            let matches = regex.matches(in: response, options: [], range: NSRange(location: 0, length: response.utf16.count))

            // 从后往前处理，避免索引变化
            for match in matches.reversed() {
                if let emotionRange = Range(match.range(at: 1), in: response) {
                    let emotionString = String(response[emotionRange])

                    // 尝试匹配支持的情感类型
                    if let emotion = CanCanEmotion(rawValue: emotionString) {
                        extractedEmotions.insert(emotion, at: 0) // 保持顺序
                    }
                }

                // 移除标签
                if let fullRange = Range(match.range, in: response) {
                    cleanContent = cleanContent.replacingCharacters(in: fullRange, with: "")
                }
            }
        } catch {
            print("❌ 情感标签解析失败: \(error)")
        }

        return (cleanContent, extractedEmotions)
    }

    /// 从对话内容分析情感状态
    private func analyzeEmotionFromContent(_ content: String) -> EmotionState {
        // 简单的情感分析逻辑
        if content.contains("😊") || content.contains("哈哈") || content.contains("开心") {
            return EmotionState(primary: .happy, intensity: 0.8, secondary: [.playful])
        } else if content.contains("😢") || content.contains("难过") || content.contains("伤心") {
            return EmotionState(primary: .concerned, intensity: 0.7, secondary: [.empathetic])
        } else if content.contains("😍") || content.contains("喜欢") || content.contains("爱") {
            return EmotionState(primary: .caring, intensity: 0.9, secondary: [.happy])
        } else {
            return EmotionState(primary: .calm, intensity: 0.6, secondary: [.friendly])
        }
    }



    // MARK: - 辅助方法

    /// 生成记忆上下文
    private func generateMemoryContext(_ memories: [ChatHistory]) -> String {
        guard !memories.isEmpty else { return "" }

        let memoryContext = memories.prefix(5).map { memory in
            let timeStr = memory.timestamp.formatted(date: .abbreviated, time: .shortened)
            let content = memory.imageDescription ?? memory.content
            return "[\(timeStr)] \(content)"
        }.joined(separator: "\n")

        return """

        === 相关对话记忆 ===
        \(memoryContext)
        === 记忆结束 ===

        请结合以上对话记忆来回答用户的问题。
        """
    }
}
